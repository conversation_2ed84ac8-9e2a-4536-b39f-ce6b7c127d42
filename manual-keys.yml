---
- name: Manually inject Hippius Keys
  hosts: ipfs_nodes
  gather_facts: yes
  become: yes
  tasks:
    - name: Include role-specific variables
      include_vars:
        dir: ansible/group_vars
        
    - name: Check if hippius_hotkey_mnemonic is provided
      fail:
        msg: "Please provide hippius_hotkey_mnemonic using -e or in the inventory/group_vars"
      when: hippius_hotkey_mnemonic is not defined or hippius_hotkey_mnemonic == ""
    
    # Create Hippius directories and download binary  
    - name: Include basic Hippius setup tasks
      include_tasks: roles/hippius/tasks/main.yml
      vars:
        skip_key_injection: true
        
    # Create the service but don't start it yet
    - name: Create Hippius service
      template:
        src: roles/hippius/templates/hippius.service.j2
        dest: /etc/systemd/system/hippius.service
        mode: '0644'

    - name: Reload systemd
      systemd:
        daemon_reload: yes
      
    # Now inject the keys
    - name: Include manual key injection tasks
      include_tasks: roles/hippius/tasks/manual_key_injection.yml
      
    # Finally, start the service
    - name: Enable and start Hippius service
      systemd:
        name: hippius
        state: started
        enabled: yes
      register: service_start_result
      
    - name: Display service start result (if it failed)
      debug:
        msg: "Service start failed: {{ service_start_result }}"
      when: service_start_result is failed 