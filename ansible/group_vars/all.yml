# Docker configuration
docker_packages:
  - docker-ce
  - docker-ce-cli
  - containerd.io
  - docker-buildx-plugin
  - docker-compose-plugin

# Hippius configuration
hippius_binary_url: "https://download.hippius.com/hippius"
hippius_binary_name: "hippius"
hippius_home: /opt/hippius
hippius_data_dir: "{{ hippius_home }}/data"
hippius_binary_path: "{{ hippius_home }}/bin"
hippius_key_path: "{{ hippius_data_dir }}/chains/hippius_mainnet/network/secret_ed25519"
hippius_key: ""  # Optional: Provide an existing ED25519 key in hex format
hippius_hotkey_mnemonic: ""  # User will provide the hotkey mnemonic phrase
hippius_ports:
  rpc: 9944
  p2p: 30333
  ws: 9933 