---
- name: Install required system packages
  apt:
    name:
      - apt-transport-https
      - ca-certificates
      - curl
      - software-properties-common
      - gnupg
    state: present
    update_cache: yes
  become: yes

# - name: Add Docker GPG key
#   apt_key:
#     url: https://download.docker.com/linux/ubuntu/gpg
#     state: present
#   become: yes

# - name: Add Docker repository
#   apt_repository:
#     repo: "deb [arch=amd64] https://download.docker.com/linux/ubuntu {{ ansible_distribution_release }} stable"
#     state: present
#   become: yes

# - name: Update apt and install docker-ce
#   apt:
#     name: docker-ce
#     state: present
#     update_cache: yes
#   become: yes

# - name: Install docker-compose-plugin
#   apt:
#     name: docker-compose-plugin
#     state: present
#   become: yes

- name: Check Docker service status
  command: systemctl status docker
  register: docker_status
  changed_when: false
  ignore_errors: yes
  become: yes

- name: Reset Docker service
  systemd:
    name: docker
    state: stopped
    daemon_reload: yes
  become: yes
  when: docker_status.rc == 0

- name: Start Docker service
  systemd:
    name: docker
    state: started
    enabled: yes
  become: yes
  register: docker_start_result
  ignore_errors: yes

- name: Wait for Dock<PERSON> to be ready
  wait_for:
    timeout: 30
  when: docker_start_result.rc == 0 and (skip_subtensor is not defined or not skip_subtensor)

- name: Create directory for subtensor
  file:
    path: "/opt/subtensor"
    state: directory
    mode: '0755'
  when: skip_subtensor is not defined or not skip_subtensor

- name: Create docker-compose.yml
  template:
    src: docker-compose.yml.j2
    dest: /opt/subtensor/docker-compose.yml
    mode: '0644'
  when: skip_subtensor is not defined or not skip_subtensor

- name: Pull subtensor image
  docker_image:
    name: "{{ subtensor_image }}"
    source: pull
    force_source: yes
  ignore_errors: yes
  register: pull_result
  when: skip_subtensor is not defined or not skip_subtensor

- name: Check if subtensor image was pulled successfully
  debug:
    msg: "Failed to pull subtensor image. Will try to build it."
  when: (skip_subtensor is not defined or not skip_subtensor) and pull_result is failed

- name: Install Docker Buildx (if pull failed)
  block:
    - name: Create ~/.docker/cli-plugins directory
      file:
        path: ~/.docker/cli-plugins
        state: directory
        mode: '0755'
      become: yes
      when: pull_result is failed
      
    - name: Download Docker Buildx
      get_url:
        url: https://github.com/docker/buildx/releases/download/v0.12.1/buildx-v0.12.1.linux-amd64
        dest: ~/.docker/cli-plugins/docker-buildx
        mode: '0755'
      become: yes
      when: pull_result is failed
      
    - name: Create Dockerfile for subtensor
      copy:
        dest: /opt/subtensor/Dockerfile
        content: |
          FROM ubuntu:22.04
          
          # Install dependencies
          RUN apt-get update && \
              apt-get install -y git build-essential curl libssl-dev pkg-config clang
          
          # Install Rust
          RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
          ENV PATH="/root/.cargo/bin:${PATH}"
          
          # Clone and build subtensor
          RUN git clone https://github.com/opentensor/subtensor.git
          WORKDIR /subtensor
          RUN cargo build --release
          
          # Setup the runtime
          VOLUME ["/data"]
          EXPOSE 9944 9933 30333
          
          ENTRYPOINT ["/subtensor/target/release/node-subtensor"]
        mode: '0644'
      become: yes
      when: pull_result is failed
      
    - name: Build subtensor image for amd64
      command: >
        docker buildx build --platform linux/amd64 -t opentensor/subtensor:latest /opt/subtensor
      become: yes
      when: pull_result is failed
  when: (skip_subtensor is not defined or not skip_subtensor) and pull_result is failed

- name: Start subtensor service
  shell: cd /opt/subtensor && docker-compose up -d mainnet-lite
  become: yes
  when: skip_subtensor is not defined or not skip_subtensor

- name: Verify subtensor service is running
  shell: docker ps | grep subtensor
  register: subtensor_status
  ignore_errors: yes
  changed_when: false
  become: yes
  when: skip_subtensor is not defined or not skip_subtensor

- name: Display subtensor status
  debug:
    var: subtensor_status.stdout
  when: skip_subtensor is not defined or not skip_subtensor and subtensor_status.rc == 0

- name: Check if we should proceed despite Docker failure
  set_fact:
    skip_subtensor: true
  when: docker_start_result.rc is defined and docker_start_result.rc != 0
  
- name: Display Docker failure warning
  debug:
    msg: "Docker failed to start. Skipping subtensor deployment. Check Docker service logs for details."
  when: skip_subtensor is defined and skip_subtensor
