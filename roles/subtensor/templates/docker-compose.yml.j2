version: "3.8"

volumes:
  {{ subtensor_volume_name }}:

services:
  mainnet-lite:
    image: {{ subtensor_image }}
    container_name: {{ subtensor_container_name }}
    cpu_count: {{ subtensor_cpu_count }}
    mem_limit: {{ subtensor_mem_limit }}
    memswap_limit: {{ subtensor_memswap_limit }}
    ports:
      - "{{ subtensor_ports.rpc }}:9944"
      - "{{ subtensor_ports.p2p }}:30333"
      - "{{ subtensor_ports.ws }}:9933"
    expose:
      - "{{ subtensor_ports.rpc }}"
      - "{{ subtensor_ports.p2p }}"
      - "{{ subtensor_ports.ws }}"
    environment:
      - CARGO_HOME=/var/www/node-subtensor/.cargo
    volumes:
      - {{ subtensor_volume_name }}:{{ subtensor_volume_path }}
    command:
      - /bin/bash
      - -c
      - |
        node-subtensor \
          --base-path {{ subtensor_volume_path }} \
          --chain {{ subtensor_chain_spec }} \
          --rpc-external --rpc-cors all \
          --no-mdns \
          --in-peers {{ subtensor_peer_count }} --out-peers {{ subtensor_peer_count }} \
          --bootnodes {{ subtensor_bootnode }} \
          --sync warp
