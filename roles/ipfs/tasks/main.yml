---
- name: Download Kubo IPFS
  get_url:
    url: "{{ ipfs_download_url }}"
    dest: /tmp/kubo.tar.gz
    mode: '0755'
  become: yes

- name: Extract Kubo IPFS
  unarchive:
    src: /tmp/kubo.tar.gz
    dest: /tmp
    remote_src: yes
  become: yes

- name: Install Kubo IPFS
  command: bash /tmp/kubo/install.sh
  args:
    creates: /usr/local/bin/ipfs
  become: yes

- name: Create IPFS user
  user:
    name: "{{ ipfs_user }}"
    system: yes
    shell: /bin/false
    create_home: yes
    home: "{{ ipfs_home }}"
  become: yes

- name: Create IPFS directory
  file:
    path: "{{ ipfs_data_dir }}"
    state: directory
    owner: "{{ ipfs_user }}"
    group: "{{ ipfs_group }}"
    mode: '0755'
  become: yes

- name: Initialize IPFS
  shell: |
    export IPFS_PATH="{{ ipfs_data_dir }}"
    if [ ! -f "$IPFS_PATH/config" ]; then
      ipfs init --profile=server
      ipfs config Addresses.API {{ ipfs_api_address }}
      ipfs config Addresses.Gateway {{ ipfs_gateway_address }}
    fi
  become: yes
  args:
    creates: "{{ ipfs_data_dir }}/config"

- name: Fix IPFS directory permissions
  file:
    path: "{{ ipfs_data_dir }}"
    owner: "{{ ipfs_user }}"
    group: "{{ ipfs_group }}"
    recurse: yes
    mode: '0755'
  become: yes

- name: Create IPFS systemd service
  template:
    src: ipfs.service.j2
    dest: /etc/systemd/system/ipfs.service
    mode: '0644'
  become: yes

- name: Reload systemd
  systemd:
    daemon_reload: yes
  become: yes

- name: Enable and start IPFS service
  systemd:
    name: ipfs
    state: started
    enabled: yes
  become: yes
