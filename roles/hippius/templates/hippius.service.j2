[Unit]
Description=Hippius TheBrain Node
After=network.target

[Service]
Type=simple
User=root
Group=root
ExecStart={{ hippius_binary_path }}/{{ hippius_binary_name }} \
    --offchain-worker Always \
    --validator \
    --base-path {{ hippius_data_dir }} \
    --port {{ hippius_ports.p2p }} \
    --rpc-cors all \
    --database paritydb \
    --name {{ hippius_node_name }} \
    --telemetry-url "{{ hippius_telemetry_url }}" \
    --chain hippius \
    --node-key-file {{ hippius_key_path }} \
    --pruning archive \
    --no-mdns \
    --in-peers 150 \
    --out-peers 150 \
    --bootnodes /ip4/***************/tcp/30333/ws/p2p/12D3KooWAXNTAcp2d8rFG6iW43nYhkciWepUFJxr8yzZbELyYByb     


Restart=always
RestartSec={{ service_restart_delay }}
LimitNOFILE={{ service_nofile_limit }}

[Install]
WantedBy=multi-user.target
