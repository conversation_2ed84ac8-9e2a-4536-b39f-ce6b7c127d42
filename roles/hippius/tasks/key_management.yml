---
- name: Ensure network directory exists
  file:
    path: "{{ hippius_key_path | dirname }}"
    state: directory
    owner: root
    group: root
    mode: '0700'

- name: Check if key exists
  stat:
    path: "{{ hippius_key_path }}"
  register: key_file

- name: Generate node key using Hippius binary if not provided and not exists
  block:
    - name: Ensure parent directory exists for key
      file:
        path: "{{ hippius_key_path | dirname }}"
        state: directory
        owner: root
        group: root
        mode: '0700'
      when: not key_file.stat.exists and hippius_key == ""

    - name: Generate node key using Hippius binary
      command: "{{ hippius_binary_path }}/{{ hippius_binary_name }} key generate-node-key --file {{ hippius_key_path }}"
      register: generate_key_result
      when: not key_file.stat.exists and hippius_key == ""

    - name: Debug output of key generation
      debug:
        var: generate_key_result
      when: not key_file.stat.exists and hippius_key == "" and generate_key_result is defined
  when: not key_file.stat.exists and hippius_key == ""

- name: Store provided key if exists
  copy:
    content: "{{ hippius_key }}"
    dest: "{{ hippius_key_path }}"
    owner: root
    group: root
    mode: '0600'
  when: not key_file.stat.exists and hippius_key != ""
